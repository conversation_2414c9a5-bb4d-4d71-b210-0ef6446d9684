{"a": {"type": "typeA", "persist": true}, "b": {"type": "typeA", "persist": true}, "ulz4i7qeummmnflpl3op": {"nodes": [{"id": "com.flow.input_1745302946079", "typeUID": "com.flow.input", "position": {"x": 40, "y": 40}, "size": {"width": 200, "height": 200}}, {"id": "com.flow.output_1745303842692", "typeUID": "com.flow.output", "position": {"x": 560, "y": 40}, "size": {"width": 200, "height": 200}}, {"id": "com.flow.math_1745303847459", "typeUID": "com.flow.math", "position": {"x": 300, "y": 40}, "size": {"width": 200, "height": 200}}], "connections": [{"id": "conn_1745303853418", "sourceNodeId": "com.flow.math_1745303847459", "sourcePortId": "result", "targetNodeId": "com.flow.output_1745303842692", "targetPortId": "in1"}, {"id": "conn_1745303854982", "sourceNodeId": "com.flow.input_1745302946079", "sourcePortId": "out1", "targetNodeId": "com.flow.math_1745303847459", "targetPortId": "a"}], "zoom": 1.0150191638707133, "pan": {"x": 4.7708377784904314, "y": 105.67046058458018}, "id": "ulz4i7qeummmnflpl3op", "path": "newFlow1", "notes": "", "persist": true, "type": "RootFlow"}, "0zcznr4lvplqmdy4l1yttm8": {"nodes": [{"id": "com.flow.button_1747079146684", "typeUID": "com.flow.button", "position": {"x": 20, "y": 60}, "size": {"width": 200, "height": 200}}, {"id": "com.flow.output_1747079147910", "typeUID": "com.flow.output", "position": {"x": 300, "y": 60}, "size": {"width": 200, "height": 200}}, {"id": "com.flow.input_1748189843067", "typeUID": "com.flow.input", "position": {"x": 20, "y": 300}, "size": {"width": 200, "height": 200}}, {"id": "com.flow.output_1748189847293", "typeUID": "com.flow.output", "position": {"x": 840, "y": 60}, "size": {"width": 200, "height": 200}}, {"id": "com.flow.function_1748288494158", "typeUID": "com.flow.function", "position": {"x": 580, "y": 60}, "size": {"width": 200, "height": 200}, "config": {"ins": {"backendCode": {"value": "// Function Node Backend Code\n// Available variables:\n// - context: The node context\n// - ins: Access to input values\n// - outs: Methods to set output values\n\n// Example: Process input and set output\n\n// Log something\nconsole.log('Function Node Code executed.');\nlet count = 0\nsetInterval(()=>{\n  console.log(\"EY!\", count++)\n}, 2000)\n"}, "nodeUICode": {"value": "// Function Node UI Code\n// This code will be used to create a custom UI component\n// for this function node.\n\n// Example:\nexport default {\n  setup(props) {\n    // Access node context\n    const { context } = props;\n    \n    // Return template and methods\n    return {\n      // Your component logic here\n    };\n  }\n};\n"}, "inputDefinitions": {"value": {"input1": {"valueType": "any", "description": "Input description"}}}, "outputDefinitions": {"value": {"output1": {"valueType": "any", "description": "Output description"}}}}}}], "connections": [{"id": "conn_1748169585444", "sourceNodeId": "com.flow.button_1747079146684", "sourcePortId": "up", "targetNodeId": "com.flow.output_1747079147910", "targetPortId": "in1"}, {"id": "conn_1748792306652", "sourceNodeId": "com.flow.function_1748288494158", "sourcePortId": "_log", "targetNodeId": "com.flow.output_1748189847293", "targetPortId": "in1"}], "zoom": 1.0460890548900006, "pan": {"x": 0.5305145581373836, "y": -4.233691226993869}, "id": "0zcznr4lvplqmdy4l1yttm8", "path": "defaultPath/defaultFlow", "notes": "Default flow created when no flow is known.", "persist": true, "type": "RootFlow"}}