{"name": "@webapp/backend", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "tsx watch --clear-screen=false src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest --watch", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf dist node_modules"}, "dependencies": {"@webapp/common": "workspace:*", "cors": "^2.8.5", "express": "^4.18.2", "mqtt": "^5.3.3", "ws": "^8.18.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/mqtt": "^2.5.0", "@types/node": "^20.10.0", "@types/ws": "^8.5.10", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsx": "^4.7.1", "typescript": "^5.3.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transform": {"^.+\\.tsx?$": ["ts-jest", {"useESM": true}]}}}