{"name": "fullstack-vue-express", "version": "1.0.0", "private": true, "scripts": {"dev": "concurrently \"pnpm --filter backend dev\" \"pnpm --filter frontend dev\" \"pnpm --filter common watch\"", "build": "pnpm --filter common build && pnpm --filter backend build && pnpm --filter frontend build", "start": "cross-env NODE_ENV=production pnpm --filter backend start", "install:all": "pnpm install", "clean": "pnpm --filter \"*\" clean && rm -rf node_modules"}, "devDependencies": {"@types/ws": "^8.5.14", "concurrently": "^8.2.2", "cross-env": "^7.0.3"}}